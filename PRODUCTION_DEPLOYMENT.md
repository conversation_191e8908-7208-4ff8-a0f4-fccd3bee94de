# 生产环境部署指南

## 📋 已清理的非生产环境文件

### ✅ 已移除的文件和内容：

1. **版本控制文件**
   - `.git` 目录（如果存在）

2. **测试和调试代码**
   - `/api/test-calculation` 测试API端点
   - server.js中的所有调试日志（console.log）
   - 开发用的详细错误输出

3. **开发依赖**
   - `mongodb` 包（项目实际使用PostgreSQL）
   - package.json中的 `dev` 和 `test` 脚本

4. **调试信息**
   - 详细的启动日志
   - 数据处理过程中的调试输出
   - API端点列表输出

## 🚀 生产环境部署步骤

### 1. 环境准备
```bash
# 确保Node.js版本 >= 16
node --version

# 确保PostgreSQL已安装并运行
psql --version
```

### 2. 安装依赖
```bash
# 仅安装生产依赖
npm install --production

# 或者使用yarn
yarn install --production
```

### 3. 数据库配置
确保PostgreSQL数据库配置正确：
- 数据库名：`pig_breeding`
- 用户权限：具有创建表的权限
- 连接参数在server.js中配置

### 4. 启动应用
```bash
# 生产环境启动
npm start

# 或者使用PM2进行进程管理
pm2 start server.js --name "pig-breeding-system"
```

### 5. 验证部署
- 访问 `http://localhost:3000` 确认应用正常运行
- 测试Excel文件上传功能
- 验证数据库连接和查询功能

## 📁 生产环境文件结构

```
/
├── node_modules/          # 生产依赖
├── public/               # 前端静态文件
│   ├── index.html       # 主页面
│   └── app.js           # 前端JavaScript
├── uploads/             # Excel文件上传目录
├── package.json         # 项目配置（已清理）
├── package-lock.json    # 依赖锁定文件
└── server.js           # 主服务器文件（已清理调试代码）
```

## 🔧 生产环境优化建议

### 1. 进程管理
使用PM2或类似工具管理Node.js进程：
```bash
npm install -g pm2
pm2 start server.js --name pig-breeding
pm2 startup
pm2 save
```

### 2. 反向代理
使用Nginx作为反向代理：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. 环境变量
创建 `.env` 文件管理环境配置：
```env
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pig_breeding
DB_USER=your_user
DB_PASSWORD=your_password
```

### 4. 日志管理
- 使用winston或类似库进行结构化日志
- 配置日志轮转避免磁盘空间问题
- 设置错误监控和告警

### 5. 安全配置
- 配置HTTPS
- 设置适当的CORS策略
- 添加请求限制和安全头
- 定期更新依赖包

## 🔍 监控和维护

### 1. 健康检查
添加健康检查端点：
```javascript
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});
```

### 2. 性能监控
- 监控内存使用情况
- 监控数据库连接池状态
- 监控API响应时间

### 3. 备份策略
- 定期备份PostgreSQL数据库
- 备份上传的Excel文件
- 制定灾难恢复计划

## ⚠️ 注意事项

1. **数据库连接**：确保PostgreSQL服务正常运行
2. **文件权限**：确保uploads目录有写入权限
3. **内存管理**：监控大文件上传时的内存使用
4. **并发处理**：根据服务器配置调整数据库连接池大小
5. **错误处理**：生产环境中错误信息已简化，避免泄露敏感信息

## 📞 故障排除

### 常见问题：
1. **数据库连接失败**：检查PostgreSQL服务状态和连接参数
2. **文件上传失败**：检查uploads目录权限
3. **内存不足**：调整Node.js内存限制或优化数据处理逻辑
4. **端口占用**：检查端口3000是否被其他进程占用

### 日志位置：
- 应用日志：控制台输出或配置的日志文件
- 系统日志：/var/log/（Linux）或事件查看器（Windows）
- PM2日志：`pm2 logs pig-breeding`
