<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>巨朵小猪配种查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* 允许输入框和表格内容选择 */
        input, textarea, table {
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }
        
        .container {
            
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
        }

        .header h1 {
            font-size: 18px;
            margin-bottom: 8px;
        }
        
        .upload-section {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .upload-section h2 {
            margin-bottom: 8px;
            color: #333;
        }

        .upload-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }
        
        .file-input {
            display: none;
        }
        
        .file-input-label {
            display: inline-block;
            padding: 6px 12px;
            background-color: #6c757d;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 12px;
            white-space: nowrap;
        }

        .file-input-label:hover {
            background-color: #5a6268;
        }

        .upload-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 12px;
            white-space: nowrap;
        }

        .upload-btn:hover {
            background-color: #218838;
        }

        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .template-btn {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 12px;
            white-space: nowrap;
        }

        .template-btn:hover {
            background-color: #138496;
        }



        .search-container {
            margin-bottom: 10px;
            padding: 6px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .search-row {
            display: flex;
            gap: 5px;
            align-items: center;
            flex-wrap: nowrap;
        }

        .search-input {
            flex: 1;
            min-width: 0;
            padding: 16px 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: border-color 0.3s, box-shadow 0.3s;
            background-color: white;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }

        .menu-select {
            min-width: 120px;
            flex-shrink: 0;
            padding: 16px 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            background-color: white;
            cursor: pointer;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .menu-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }

        .search-message {
            min-height: 14px;
            margin-top: 6px;
        }

        .search-message .info {
            margin: 3px 0;
            padding: 4px 6px;
            font-size: 8px;
        }

        .search-message .success {
            margin: 3px 0;
            padding: 4px 6px;
            font-size: 8px;
        }

        .search-message .error {
            margin: 3px 0;
            padding: 4px 6px;
            font-size: 8px;
        }

        .search-message .loading {
            margin: 3px 0;
            padding: 4px 6px;
            font-size: 8px;
        }

        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 4px 6px;
            border-radius: 3px;
            margin: 3px 0;
            font-size: 8px;
        }

        .all-records-section {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }




        
        .record-count {
            text-align: center;
            padding: 8px;
            margin-top: 10px;
        }

        .record-count span {
            font-size: 11px;
            color: #6c757d;
            font-weight: normal;
        }
        
        .data-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }
        
        .data-section.active {
            display: block;
        }
        
        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .back-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        
        .back-btn:hover {
            background-color: #5a6268;
        }
        
        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* 手机端滚动条优化 */
        .table-container::-webkit-scrollbar {
            height: 4px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            table-layout: fixed;
            min-width: 1400px;
        }

        th, td {
            padding: 8px 4px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 列宽优化 - 确保数据完整显示 */
        th:nth-child(1), td:nth-child(1) { width: 100px; min-width: 100px; }   /* 母猪耳号 */
        th:nth-child(2), td:nth-child(2) { width: 80px; min-width: 80px; }     /* 配时状态 */
        th:nth-child(3), td:nth-child(3) { width: 50px; min-width: 50px; }     /* 胎次 */
        th:nth-child(4), td:nth-child(4) { width: 90px; min-width: 90px; }     /* 天数/状态 */
        th:nth-child(5), td:nth-child(5) { width: 100px; min-width: 100px; }   /* 配种日期 */
        th:nth-child(6), td:nth-child(6) { width: 100px; min-width: 100px; }   /* 首配公猪 */
        th:nth-child(7), td:nth-child(7) { width: 100px; min-width: 100px; }   /* 次配公猪 */
        th:nth-child(8), td:nth-child(8) { width: 80px; min-width: 80px; }     /* 首配人 */
        th:nth-child(9), td:nth-child(9) { width: 80px; min-width: 80px; }     /* 次配人 */
        th:nth-child(10), td:nth-child(10) { width: 90px; min-width: 90px; }   /* 25天妊检 */
        th:nth-child(11), td:nth-child(11) { width: 90px; min-width: 90px; }   /* 异常状态 */
        th:nth-child(12), td:nth-child(12) { width: 100px; min-width: 100px; } /* 异常日期 */
        th:nth-child(13), td:nth-child(13) { width: 100px; min-width: 100px; } /* 预产日期 */
        th:nth-child(14), td:nth-child(14) { width: 60px; min-width: 60px; }   /* 栋舍 */
        th:nth-child(15), td:nth-child(15) { width: 60px; min-width: 60px; }   /* 栏位 */
        th:nth-child(16), td:nth-child(16) { width: 120px; min-width: 120px; } /* 备注 */
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }

        /* 数据行可点击样式 */
        .data-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .data-row:hover {
            background-color: #e3f2fd !important;
        }

        .data-row:active {
            background-color: #bbdefb !important;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 4px 6px;
            border-radius: 3px;
            margin: 3px 0;
            font-size: 8px;
        }

        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 4px 6px;
            border-radius: 3px;
            margin: 3px 0;
            font-size: 8px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background-color: #ffffff;
            margin: 15px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            width: 50%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #000000;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #000000;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background-color: #f0f0f0;
            color: #000000;
        }

        .modal-body {
            font-size: 14px;
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
            align-items: start;
        }

        .detail-item {
            display: flex;
            margin-bottom: 4px;
            padding: 4px 6px;
            background-color: #ffffff;
            border-radius: 3px;
            align-items: center;
            border-left: 2px solid #007bff;
            transition: background-color 0.2s;
        }

        .detail-item:hover {
            background-color: #f8f8f8;
        }

        .detail-label {
            font-weight: 600;
            color: #000000;
            min-width: 80px;
            flex-shrink: 0;
            margin-right: 8px;
            font-size: 13px;
        }

        .detail-value {
            color: #000000;
            flex: 1;
            word-break: break-all;
            font-size: 13px;
        }

        .detail-value.empty {
            color: #666666;
            font-style: italic;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid #e9ecef;
            gap: 8px;
            flex-wrap: wrap;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 12px;
            margin-right: 15px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background-color: white;
            color: #495057;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
            min-width: 35px;
            text-align: center;
        }

        .pagination-btn:hover {
            background-color: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        .pagination-btn.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }

        .pagination-btn:disabled {
            background-color: #f8f9fa;
            border-color: #ddd;
            color: #6c757d;
            cursor: not-allowed;
        }

        .pagination-btn:disabled:hover {
            background-color: #f8f9fa;
            border-color: #ddd;
            color: #6c757d;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-left: 15px;
            font-size: 12px;
            color: #6c757d;
        }

        .page-size-select {
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 12px;
            background-color: white;
        }

        .pagination-ellipsis {
            color: #6c757d;
            padding: 6px 4px;
            font-size: 12px;
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 6px;
            }

            .header {
                padding: 10px;
                margin-bottom: 10px;
            }

            .header h1 {
                font-size: 20px;
                margin-bottom: 8px;
            }

            .upload-actions {
                gap: 6px;
                flex-wrap: wrap;
            }

            .file-input-label, .upload-btn, .template-btn {
                padding: 8px 12px;
                font-size: 14px;
            }

            .all-records-section {
                padding: 10px;
                margin-bottom: 10px;
            }



            .search-container {
                margin-bottom: 6px;
                padding: 4px;
            }

            .search-row {
                gap: 3px;
            }

            .search-input, .menu-select {
                font-size: 18px;
                padding: 16px 12px;
            }

            .menu-select {
                min-width: 120px;
                flex-shrink: 0;
            }

            .record-count {
                padding: 8px;
                margin-top: 10px;
            }

            .record-count span {
                font-size: 13px;
            }

            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            table {
                font-size: 12px;
                min-width: 1200px;
                width: 100%;
            }

            th, td {
                padding: 6px 3px;
                white-space: nowrap;
                font-size: 12px;
            }

            th {
                font-size: 11px;
                font-weight: 600;
            }

            /* 手机端列宽优化 - 保持数据完整显示 */
            th:nth-child(1), td:nth-child(1) { width: 90px; min-width: 90px; }   /* 母猪耳号 */
            th:nth-child(2), td:nth-child(2) { width: 70px; min-width: 70px; }   /* 配时状态 */
            th:nth-child(3), td:nth-child(3) { width: 40px; min-width: 40px; }   /* 胎次 */
            th:nth-child(4), td:nth-child(4) { width: 80px; min-width: 80px; }   /* 天数/状态 */
            th:nth-child(5), td:nth-child(5) { width: 90px; min-width: 90px; }   /* 配种日期 */
            th:nth-child(6), td:nth-child(6) { width: 90px; min-width: 90px; }   /* 首配公猪 */
            th:nth-child(7), td:nth-child(7) { width: 90px; min-width: 90px; }   /* 次配公猪 */
            th:nth-child(8), td:nth-child(8) { width: 70px; min-width: 70px; }   /* 首配人 */
            th:nth-child(9), td:nth-child(9) { width: 70px; min-width: 70px; }   /* 次配人 */
            th:nth-child(10), td:nth-child(10) { width: 80px; min-width: 80px; } /* 25天妊检 */
            th:nth-child(11), td:nth-child(11) { width: 80px; min-width: 80px; } /* 异常状态 */
            th:nth-child(12), td:nth-child(12) { width: 90px; min-width: 90px; } /* 异常日期 */
            th:nth-child(13), td:nth-child(13) { width: 90px; min-width: 90px; } /* 预产日期 */
            th:nth-child(14), td:nth-child(14) { width: 50px; min-width: 50px; } /* 栋舍 */
            th:nth-child(15), td:nth-child(15) { width: 50px; min-width: 50px; } /* 栏位 */
            th:nth-child(16), td:nth-child(16) { width: 100px; min-width: 100px; } /* 备注 */

            /* 移动端模态框优化 */
            .modal-content {
                margin: 8px;
                padding: 12px;
                width: 90%;
                max-width: 90vw;
                max-height: 92vh;
            }

            .modal-body {
                grid-template-columns: 1fr;
                gap: 6px;
            }

            .modal-title {
                font-size: 15px;
            }

            .detail-item {
                margin-bottom: 3px;
                padding: 3px 6px;
            }

            .detail-label {
                min-width: 70px;
                margin-right: 6px;
                font-size: 12px;
            }

            .detail-value {
                font-size: 12px;
            }

            /* 移动端分页优化 */
            .pagination {
                margin-top: 15px;
                padding: 10px 0;
                gap: 5px;
            }

            .pagination-info {
                font-size: 13px;
                margin-right: 12px;
            }

            .pagination-btn {
                padding: 8px 10px;
                font-size: 13px;
                min-width: 35px;
            }

            .page-size-selector {
                margin-left: 12px;
                font-size: 13px;
            }

            .page-size-select {
                padding: 6px 8px;
                font-size: 13px;
            }

            /* 移动端提示信息字体调整 */
            .search-message .info,
            .search-message .success,
            .search-message .error,
            .search-message .loading,
            .info,
            .success {
                font-size: 12px;
                padding: 6px 8px;
            }

        }
        
        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .container {
                padding: 4px;
            }

            .header {
                padding: 8px;
                margin-bottom: 8px;
            }

            .header h1 {
                font-size: 18px;
                margin-bottom: 6px;
            }

            .upload-actions {
                gap: 4px;
                flex-direction: row;
                justify-content: center;
            }

            .file-input-label, .upload-btn, .template-btn {
                padding: 6px 10px;
                font-size: 12px;
            }

            .all-records-section {
                padding: 8px;
                margin-bottom: 8px;
            }



            .search-container {
                margin-bottom: 5px;
                padding: 4px;
            }

            .search-row {
                gap: 2px;
            }

            .search-input, .menu-select {
                font-size: 16px;
                padding: 14px 10px;
            }

            .menu-select {
                min-width: 110px;
                flex-shrink: 0;
            }

            .record-count {
                padding: 6px;
                margin-top: 8px;
            }

            .record-count span {
                font-size: 11px;
            }

            table {
                font-size: 15px;
                min-width: 1100px;
            }

            th, td {
                padding: 10px 6px;
            }

            th {
                font-size: 14px;
            }

            /* 超小屏幕模态框优化 */
            .modal-content {
                margin: 4px;
                padding: 10px;
                width: 95%;
                max-width: 95vw;
                max-height: 96vh;
            }

            .modal-body {
                grid-template-columns: 1fr;
                gap: 4px;
            }

            .modal-title {
                font-size: 14px;
            }

            .detail-item {
                margin-bottom: 2px;
                padding: 2px 4px;
            }

            .detail-label {
                min-width: 60px;
                margin-right: 6px;
                font-size: 11px;
            }

            .detail-value {
                font-size: 11px;
            }

            /* 超小屏幕分页优化 */
            .pagination {
                margin-top: 10px;
                padding: 8px 0;
                gap: 3px;
                flex-direction: column;
            }

            .pagination-info {
                font-size: 12px;
                margin-right: 0;
                margin-bottom: 6px;
            }

            .pagination-btn {
                padding: 6px 8px;
                font-size: 12px;
                min-width: 30px;
            }

            .page-size-selector {
                margin-left: 0;
                margin-top: 6px;
                font-size: 12px;
            }

            .page-size-select {
                padding: 4px 6px;
                font-size: 12px;
            }


        }

        /* 用于拖拽经过时高亮目标列的样式 */
        .drag-over {
            background-color: #e3f2fd !important; /* 使用 !important 以确保覆盖其他背景色样式 */
            border-left: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>巨朵小猪配种查询系统</h1>
            <div class="upload-actions">
                <div class="file-input-wrapper">
                    <input type="file" id="excelFile" class="file-input" accept=".xlsx,.xls">
                    <label for="excelFile" class="file-input-label">选择文件</label>
                </div>
                <button id="uploadBtn" class="upload-btn" disabled>上传</button>
                <button id="downloadTemplateBtn" class="template-btn">模板</button>
            </div>
            <div id="uploadMessage"></div>
        </div>

        <div class="all-records-section">
            <div class="search-container">
                <div class="search-row">
                    <input type="text" id="searchInput" class="search-input" placeholder="输入母猪耳号搜索">
                    <select id="menuSelect" class="menu-select">
                        <option value="">选择查询类型</option>
                    </select>
                </div>
                <div id="searchMessage" class="search-message"></div>
            </div>

            <div class="table-container">
                <table id="allRecordsTable">
                    <thead id="allRecordsTableHead"></thead>
                    <tbody id="allRecordsTableBody">
                        <tr><td colspan="16" class="loading">正在加载所有记录...</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="record-count">
                <span id="allRecordsCount">加载中...</span>
            </div>

            <!-- 分页控件 -->
            <div id="pagination" class="pagination" style="display: none;">
                <div class="pagination-info">
                    <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
                </div>
                <button id="firstPageBtn" class="pagination-btn">首页</button>
                <button id="prevPageBtn" class="pagination-btn">上一页</button>
                <div id="pageNumbers" class="page-numbers"></div>
                <button id="nextPageBtn" class="pagination-btn">下一页</button>
                <button id="lastPageBtn" class="pagination-btn">末页</button>
                <div class="page-size-selector">
                    <span>每页</span>
                    <select id="pageSizeSelect" class="page-size-select">
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                    <span>条</span>
                </div>
            </div>
        </div>
        
        <div id="dataSection" class="data-section">
            <div class="data-header">
                <button id="backBtn" class="back-btn">返回菜单</button>
                <h2 id="dataTitle"></h2>
                <span id="dataCount" class="count"></span>
            </div>
            <div class="table-container">
                <table id="dataTable">
                    <thead id="tableHead"></thead>
                    <tbody id="tableBody"></tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="recordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">记录详情</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详细信息将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="app.js?v=13"></script>
</body>
</html>
