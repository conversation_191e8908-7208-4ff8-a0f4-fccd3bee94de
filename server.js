const express = require('express');
const multer = require('multer');
const xlsx = require('xlsx');
const { Pool } = require('pg');
const cors = require('cors');
const fs = require('fs-extra');

const app = express();
const port = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// PostgreSQL 连接配置
const pool = new Pool({
  host: 'o.xxxg.net',
  port: 5432,
  database: 'pig_db',
  user: 'pig_db',
  password: '198497',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
});

// 文件上传配置
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// 确保上传目录存在
fs.ensureDirSync('uploads');

// 连接PostgreSQL并创建表
async function connectPostgreSQL() {
  try {
    console.log('🔗 尝试连接PostgreSQL数据库...');
    console.log('📍 连接信息:', {
      host: pool.options.host,
      port: pool.options.port,
      database: pool.options.database,
      user: pool.options.user
    });

    // 测试连接
    const client = await pool.connect();
    console.log('✅ PostgreSQL连接成功');

    // 创建表（如果不存在）
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS pig_records (
        id SERIAL PRIMARY KEY,
        pig_ear_number VARCHAR(50),
        breeding_status VARCHAR(50),
        parity VARCHAR(20),
        days_status VARCHAR(50),
        days_status_original VARCHAR(50),
        breeding_date DATE,
        first_boar VARCHAR(50),
        second_boar VARCHAR(50),
        first_person VARCHAR(50),
        second_person VARCHAR(50),
        pregnancy_check_25 VARCHAR(50),
        abnormal_status VARCHAR(50),
        abnormal_date DATE,
        expected_date DATE,
        building VARCHAR(50),
        pen VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_pig_ear_number ON pig_records(pig_ear_number);
      CREATE INDEX IF NOT EXISTS idx_breeding_date ON pig_records(breeding_date);
      CREATE INDEX IF NOT EXISTS idx_days_status ON pig_records(days_status);
    `;

    await client.query(createTableQuery);
    console.log('✅ 数据表创建/检查完成');

    client.release();
    return;
  } catch (error) {
    console.error('❌ PostgreSQL连接失败:', error.message);
    console.error('❌ 错误详情:', error);
    throw error;
  }
}

// 清空数据表
async function clearTable() {
  try {
    const client = await pool.connect();
    const result = await client.query('DELETE FROM pig_records');
    console.log(`🗑️ 已清空 ${result.rowCount} 条记录`);
    client.release();
  } catch (err) {
    console.error('清空数据失败:', err);
    throw err;
  }
}

// 解析Excel并导入数据
async function importExcelData(filePath) {
  try {
    // 清空现有数据
    await clearTable();

    // 读取Excel文件
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`Excel文件包含 ${data.length} 行数据`);
    console.log('前几行数据:', data.slice(0, 3));

    // 分析表头结构
    if (data.length > 0) {
      console.log('表头分析:');
      data[0].forEach((header, index) => {
        console.log(`  列${index}: ${header}`);
      });
    }

    let importCount = 0;
    const documents = [];

    // 跳过标题行，从第二行开始处理数据
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (!row || row.length === 0 || !row[0]) continue; // 跳过空行或没有母猪耳号的行

      // 处理日期字段
      let breedingDate = null;
      let abnormalDate = null;

      // 配种日期 (E列，索引4) - 北京时间处理
      if (row[4]) {
        try {
          if (typeof row[4] === 'number') {
            // Excel日期序列号转换为JavaScript日期 - 使用UTC避免时区问题
            // Excel的基准日期是1899年12月30日
            const excelEpoch = Date.UTC(1899, 11, 30); // 1899年12月30日 UTC
            const milliseconds = row[4] * 24 * 60 * 60 * 1000;
            const utcDate = new Date(excelEpoch + milliseconds);

            // 使用UTC日期组件创建本地日期，避免时区转换问题
            breedingDate = new Date(utcDate.getUTCFullYear(), utcDate.getUTCMonth(), utcDate.getUTCDate());
          } else if (typeof row[4] === 'string') {
            // 字符串日期 - 按北京时间解析
            const dateStr = row[4].toString().trim();
            if (dateStr.includes('/')) {
              // 处理 YYYY/MM/DD 格式（北京时间）
              const parts = dateStr.split('/');
              if (parts.length === 3) {
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // JavaScript月份从0开始
                const day = parseInt(parts[2]);
                breedingDate = new Date(year, month, day); // 北京时间
              }
            } else if (dateStr.includes('-')) {
              // 处理 YYYY-MM-DD 格式（北京时间）
              const parts = dateStr.split('-');
              if (parts.length === 3) {
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1;
                const day = parseInt(parts[2]);
                breedingDate = new Date(year, month, day); // 北京时间
              }
            } else {
              // 其他格式，尝试直接解析后转为北京时间日期
              const tempDate = new Date(dateStr);
              if (!isNaN(tempDate.getTime())) {
                breedingDate = new Date(tempDate.getFullYear(), tempDate.getMonth(), tempDate.getDate());
              }
            }
          } else if (row[4] instanceof Date) {
            // 已经是日期对象 - 智能处理时区
            const originalDate = row[4];

            // 检查是否是UTC时间（通过比较本地时间和UTC时间的日期部分）
            const localDatePart = originalDate.getDate();
            const utcDatePart = originalDate.getUTCDate();

            if (localDatePart === utcDatePart) {
              // 本地日期和UTC日期相同，使用本地组件
              breedingDate = new Date(originalDate.getFullYear(), originalDate.getMonth(), originalDate.getDate());
            } else {
              // 本地日期和UTC日期不同，可能是时区问题，需要判断哪个更合理
              // 通常Excel导出的日期是本地时间，所以优先使用本地组件
              breedingDate = new Date(originalDate.getFullYear(), originalDate.getMonth(), originalDate.getDate());

              // 调试信息
              console.log(`第${i+1}行日期时区检测: 本地=${localDatePart}, UTC=${utcDatePart}, 使用本地组件`);
            }
          }

          // 验证日期是否有效
          if (breedingDate && isNaN(breedingDate.getTime())) {
            console.log(`第${i+1}行配种日期无效:`, row[4]);
            breedingDate = null;
          } else if (breedingDate) {
            // 调试日志：显示原始值和解析结果
            console.log(`第${i+1}行配种日期解析: 原始值=${row[4]} (${typeof row[4]}) -> 解析结果=${breedingDate.toLocaleDateString('zh-CN')}`);
          }
        } catch (e) {
          console.log(`第${i+1}行配种日期解析失败:`, row[4], e.message);
          breedingDate = null;
        }
      }

      // 异常日期 (L列，索引11) - 北京时间处理
      if (row[11]) {
        try {
          if (typeof row[11] === 'number') {
            // Excel日期序列号转换为JavaScript日期 - 使用UTC避免时区问题
            // Excel的基准日期是1899年12月30日
            const excelEpoch = Date.UTC(1899, 11, 30); // 1899年12月30日 UTC
            const milliseconds = row[11] * 24 * 60 * 60 * 1000;
            const utcDate = new Date(excelEpoch + milliseconds);

            // 使用UTC日期组件创建本地日期，避免时区转换问题
            abnormalDate = new Date(utcDate.getUTCFullYear(), utcDate.getUTCMonth(), utcDate.getUTCDate());
          } else if (typeof row[11] === 'string') {
            // 字符串日期 - 按北京时间解析
            const dateStr = row[11].toString().trim();
            if (dateStr.includes('/')) {
              // 处理 YYYY/MM/DD 格式（北京时间）
              const parts = dateStr.split('/');
              if (parts.length === 3) {
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // JavaScript月份从0开始
                const day = parseInt(parts[2]);
                abnormalDate = new Date(year, month, day); // 北京时间
              }
            } else if (dateStr.includes('-')) {
              // 处理 YYYY-MM-DD 格式（北京时间）
              const parts = dateStr.split('-');
              if (parts.length === 3) {
                const year = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1;
                const day = parseInt(parts[2]);
                abnormalDate = new Date(year, month, day); // 北京时间
              }
            } else {
              // 其他格式，尝试直接解析后转为北京时间日期
              const tempDate = new Date(dateStr);
              if (!isNaN(tempDate.getTime())) {
                abnormalDate = new Date(tempDate.getFullYear(), tempDate.getMonth(), tempDate.getDate());
              }
            }
          } else if (row[11] instanceof Date) {
            // 已经是日期对象 - 智能处理时区
            const originalDate = row[11];

            // 检查是否是UTC时间（通过比较本地时间和UTC时间的日期部分）
            const localDatePart = originalDate.getDate();
            const utcDatePart = originalDate.getUTCDate();

            if (localDatePart === utcDatePart) {
              // 本地日期和UTC日期相同，使用本地组件
              abnormalDate = new Date(originalDate.getFullYear(), originalDate.getMonth(), originalDate.getDate());
            } else {
              // 本地日期和UTC日期不同，可能是时区问题，需要判断哪个更合理
              // 通常Excel导出的日期是本地时间，所以优先使用本地组件
              abnormalDate = new Date(originalDate.getFullYear(), originalDate.getMonth(), originalDate.getDate());

              // 调试信息
              console.log(`第${i+1}行异常日期时区检测: 本地=${localDatePart}, UTC=${utcDatePart}, 使用本地组件`);
            }
          }

          // 验证日期是否有效
          if (abnormalDate && isNaN(abnormalDate.getTime())) {
            console.log(`第${i+1}行异常日期无效:`, row[11]);
            abnormalDate = null;
          }
        } catch (e) {
          console.log(`第${i+1}行异常日期解析失败:`, row[11], e.message);
          abnormalDate = null;
        }
      }

      // 计算预产日期 = 配种日期 + 114天
      let calculatedExpectedDate = null;
      if (breedingDate && !isNaN(breedingDate.getTime())) {
        calculatedExpectedDate = new Date(breedingDate);
        calculatedExpectedDate.setDate(calculatedExpectedDate.getDate() + 114);

        // 调试日志
        if (i <= 5) { // 显示前5行的计算结果
          console.log(`第${i+1}行: 原始值=${row[4]}, 配种日期=${breedingDate.toLocaleDateString('zh-CN')}, 预产日期=${calculatedExpectedDate.toLocaleDateString('zh-CN')}`);
        }
      } else {
        if (i <= 5) {
          console.log(`第${i+1}行: 配种日期无效，无法计算预产日期。原始值:`, row[4]);
        }
      }

      // 计算天数/状态的自动判断逻辑
      let calculatedDaysStatus = '';
      let calculationLog = '';

      // 第一层逻辑：检查K列(异常状态)是否为空
      const abnormalStatus = row[10] ? String(row[10]).trim() : '';
      // 第二层逻辑：检查J列(25天妊检)的值 - 提前定义以便在调试日志中使用
      const pregnancyCheck = row[9] ? String(row[9]).trim() : '';

      if (abnormalStatus !== '') {
        calculatedDaysStatus = abnormalStatus;
        calculationLog = `第一层逻辑：异常状态不为空，使用异常状态值: ${abnormalStatus}`;
      } else {
        if (pregnancyCheck === '正常' || pregnancyCheck === '') {
          // 第三层逻辑：检查E列(配种日期)是否为空
          if (breedingDate && !isNaN(breedingDate.getTime())) {
            // 计算从配种日期到今天的天数差（使用北京时间，只比较日期部分）
            const today = new Date();
            const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const breedingDateOnly = new Date(breedingDate.getFullYear(), breedingDate.getMonth(), breedingDate.getDate());
            const timeDiff = todayDate.getTime() - breedingDateOnly.getTime();
            const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

            // 调试日志
            if (i <= 5) {
              console.log(`第${i+1}行天数计算详情:`);
              console.log(`  - 原始配种日期: ${row[4]}`);
              console.log(`  - 解析后配种日期: ${breedingDate.toLocaleDateString('zh-CN')}`);
              console.log(`  - 今天日期: ${today.toLocaleDateString('zh-CN')}`);
              console.log(`  - 配种日期(仅日期): ${breedingDateOnly.toLocaleDateString('zh-CN')}`);
              console.log(`  - 今天日期(仅日期): ${todayDate.toLocaleDateString('zh-CN')}`);
              console.log(`  - 时间差(毫秒): ${timeDiff}`);
              console.log(`  - 计算天数: ${daysDiff}天`);
            }

            // 第四层逻辑：根据天数判断分娩和断奶状态
            if (daysDiff > 118 && daysDiff <= 146) {
              calculatedDaysStatus = '分娩';
              calculationLog = `第四层逻辑：天数${daysDiff}天，大于118且小于等于146，判定为分娩`;
            } else if (daysDiff > 146) {
              calculatedDaysStatus = '断奶';
              calculationLog = `第四层逻辑：天数${daysDiff}天，大于146，判定为断奶`;
            } else {
              calculatedDaysStatus = daysDiff.toString();
              calculationLog = `第三层逻辑：25天妊检为"${pregnancyCheck}"，配种日期有效，计算天数差: ${daysDiff}天`;
            }
          } else {
            calculatedDaysStatus = '';
            calculationLog = `第三层逻辑：25天妊检为"${pregnancyCheck}"，但配种日期为空或无效`;
          }
        } else {
          // J列不是"正常"或空，返回J列的值
          calculatedDaysStatus = pregnancyCheck;
          calculationLog = `第二层逻辑：25天妊检不是"正常"或空，使用25天妊检值: ${pregnancyCheck}`;
        }
      }

      // 调试日志
      if (i <= 5) { // 显示前5行的计算结果
        console.log(`第${i+1}行 ${row[0] || '未知耳号'}: ${calculationLog}`);
        console.log(`  - 原始天数/状态: ${row[3] || '空'}`);
        console.log(`  - 计算后天数/状态: ${calculatedDaysStatus || '空'}`);
        console.log(`  - 异常状态(K列): ${abnormalStatus || '空'}`);
        console.log(`  - 25天妊检(J列): ${pregnancyCheck || '空'}`);
        console.log(`  - 配种日期(E列): ${breedingDate ? breedingDate.toLocaleDateString('zh-CN') : '空'}`);
        console.log('---');
      }

      const document = {
        pig_ear_number: row[0] ? String(row[0]) : null,        // 母猪耳号 (A列，索引0)
        breeding_status: row[1] ? String(row[1]) : null,       // 配时状态 (B列，索引1)
        parity: row[2] ? String(row[2]) : null,                // 胎次 (C列，索引2)
        days_status: calculatedDaysStatus,                     // 天数/状态 (D列) - 使用计算后的值
        days_status_original: row[3] ? String(row[3]) : null,  // 原始天数/状态 (D列原值，备份)
        breeding_date: breedingDate,                           // 配种日期 (E列，索引4)
        first_boar: row[5] ? String(row[5]) : null,            // 首配公猪 (F列，索引5)
        second_boar: row[6] ? String(row[6]) : null,           // 次配公猪 (G列，索引6)
        first_person: row[7] ? String(row[7]) : null,          // 首配人 (H列，索引7)
        second_person: row[8] ? String(row[8]) : null,         // 次配人 (I列，索引8)
        pregnancy_check_25: row[9] ? String(row[9]) : null,    // 25天妊检 (J列，索引9)
        abnormal_status: row[10] ? String(row[10]) : null,     // 异常状态 (K列，索引10)
        abnormal_date: abnormalDate,                          // 异常日期 (L列，索引11)
        expected_date: calculatedExpectedDate,                 // 预产日期 = 配种日期 + 114天 (自动计算)
        building: row[13] ? String(row[13]) : null,            // 栋舍 (N列，索引13)
        pen: row[14] ? String(row[14]) : null,                 // 栏位 (O列，索引14)
        notes: row[15] ? String(row[15]) : '',                 // 备注 (P列，索引15)
        created_at: new Date(),
        calculated_at: new Date()                              // 计算时间戳
      };

      documents.push(document);
      importCount++;
    }

    // 分批插入数据到PostgreSQL
    if (documents.length > 0) {
      console.log(`准备插入 ${documents.length} 条记录到数据库...`);

      const client = await pool.connect();
      const batchSize = 100; // 每批处理100条记录
      let totalInserted = 0;

      try {
        for (let i = 0; i < documents.length; i += batchSize) {
          const batch = documents.slice(i, i + batchSize);

          // 构建批量插入SQL
          const values = [];
          const placeholders = [];
          let paramIndex = 1;

          for (const doc of batch) {
            placeholders.push(`($${paramIndex}, $${paramIndex+1}, $${paramIndex+2}, $${paramIndex+3}, $${paramIndex+4}, $${paramIndex+5}, $${paramIndex+6}, $${paramIndex+7}, $${paramIndex+8}, $${paramIndex+9}, $${paramIndex+10}, $${paramIndex+11}, $${paramIndex+12}, $${paramIndex+13}, $${paramIndex+14}, $${paramIndex+15}, $${paramIndex+16}, $${paramIndex+17})`);
            values.push(
              doc.pig_ear_number,
              doc.breeding_status,
              doc.parity,
              doc.days_status,
              doc.days_status,  // days_status_original
              doc.breeding_date,
              doc.first_boar,
              doc.second_boar,
              doc.first_person,
              doc.second_person,
              doc.pregnancy_check_25,
              doc.abnormal_status,
              doc.abnormal_date,
              doc.expected_date,
              doc.building,
              doc.pen,
              doc.notes,
              new Date()  // created_at
            );
            paramIndex += 18;
          }

          const insertQuery = `
            INSERT INTO pig_records (
              pig_ear_number, breeding_status, parity, days_status, days_status_original,
              breeding_date, first_boar, second_boar, first_person, second_person,
              pregnancy_check_25, abnormal_status, abnormal_date, expected_date,
              building, pen, notes, created_at
            ) VALUES ${placeholders.join(', ')}
          `;

          try {
            const result = await client.query(insertQuery, values);
            totalInserted += result.rowCount;
            console.log(`✅ 批次 ${Math.floor(i/batchSize) + 1}: 成功插入 ${result.rowCount} 条记录 (总计: ${totalInserted}/${documents.length})`);
          } catch (batchError) {
            console.error(`❌ 批次 ${Math.floor(i/batchSize) + 1} 插入失败:`, batchError.message);
            // 继续处理下一批，不中断整个导入过程
          }
        }
      } finally {
        client.release();
      }

      console.log(`✅ 导入完成！总共成功导入 ${totalInserted} 条记录`);
      return { success: true, count: totalInserted };
    }

    return { success: true, count: 0 };
  } catch (err) {
    console.error('❌ 导入数据失败:', err);
    throw err;
  }
}

// 实时计算天数/状态的函数
function calculateRealTimeDaysStatus(record) {
  let calculatedDaysStatus = '';

  // 第一层逻辑：检查异常状态是否为空
  const abnormalStatus = record.abnormal_status ? String(record.abnormal_status).trim() : '';
  // 第二层逻辑：检查25天妊检的值
  const pregnancyCheck = record.pregnancy_check_25 ? String(record.pregnancy_check_25).trim() : '';

  if (abnormalStatus !== '') {
    calculatedDaysStatus = abnormalStatus;
  } else {
    if (pregnancyCheck === '正常' || pregnancyCheck === '') {
      // 第三层逻辑：检查配种日期是否为空
      if (record.breeding_date) {
        const breedingDate = new Date(record.breeding_date);
        if (!isNaN(breedingDate.getTime())) {
          // 计算从配种日期到今天的天数差（使用北京时间，只比较日期部分）
          const today = new Date();
          const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
          const breedingDateOnly = new Date(breedingDate.getFullYear(), breedingDate.getMonth(), breedingDate.getDate());
          const timeDiff = todayDate.getTime() - breedingDateOnly.getTime();
          const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

          // 第四层逻辑：根据天数判断分娩和断奶状态
          if (daysDiff > 118 && daysDiff <= 146) {
            calculatedDaysStatus = '分娩';
          } else if (daysDiff > 146) {
            calculatedDaysStatus = '断奶';
          } else {
            calculatedDaysStatus = daysDiff.toString();
          }
        } else {
          calculatedDaysStatus = '';
        }
      } else {
        calculatedDaysStatus = '';
      }
    } else {
      // 25天妊检不是"正常"或空，返回25天妊检的值
      calculatedDaysStatus = pregnancyCheck;
    }
  }

  return calculatedDaysStatus;
}

// 为记录数组实时计算天数/状态
function addRealTimeDaysStatus(records) {
  return records.map(record => {
    const realTimeDaysStatus = calculateRealTimeDaysStatus(record);
    return {
      ...record,
      days_status: realTimeDaysStatus,
      days_status_original: record.days_status, // 保留原始值
      days_status_calculated_at: new Date() // 计算时间戳
    };
  });
}

// API路由

// 上传Excel文件
app.post('/api/upload', upload.single('excel'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择Excel文件' });
    }
    
    const result = await importExcelData(req.file.path);
    
    // 删除上传的临时文件
    fs.unlinkSync(req.file.path);
    
    res.json({ message: '数据导入成功', count: result.count });
  } catch (err) {
    console.error('上传失败:', err);
    res.status(500).json({ error: '数据导入失败: ' + err.message });
  }
});

// 获取总列表
app.get('/api/records/all', async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT * FROM pig_records ORDER BY id DESC');
    const records = result.rows;
    client.release();

    const recordsWithRealTimeStatus = addRealTimeDaysStatus(records);
    res.json({ data: recordsWithRealTimeStatus, count: recordsWithRealTimeStatus.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取妊娠中列表（每个母猪耳号最新记录且天数/状态数值>=1，按天数倒序）
app.get('/api/records/statistics', async (req, res) => {
  try {
    const client = await pool.connect();

    // 使用PostgreSQL的DISTINCT ON获取每个母猪耳号的最新记录
    const query = `
      SELECT DISTINCT ON (pig_ear_number) *
      FROM pig_records
      WHERE pig_ear_number IS NOT NULL AND pig_ear_number != ''
      ORDER BY pig_ear_number, id DESC
    `;

    const result = await client.query(query);
    const records = result.rows;
    client.release();

    // 实时计算天数/状态
    const recordsWithRealTimeStatus = addRealTimeDaysStatus(records);

    // 筛选天数/状态数值>=1的记录（妊娠中）
    const pregnancyRecords = recordsWithRealTimeStatus.filter(record => {
      const daysStatus = record.days_status;
      // 检查是否为数字且>=1
      return /^[0-9]+$/.test(daysStatus) && parseInt(daysStatus) >= 1;
    });

    // 按天数倒序排列（天数大的在前）
    pregnancyRecords.sort((a, b) => parseInt(b.days_status) - parseInt(a.days_status));

    console.log(`🤰 妊娠中列表查询结果: 找到 ${pregnancyRecords.length} 条记录`);
    console.log(`📅 筛选条件: 每个母猪耳号最新记录且天数/状态数值>=1`);

    res.json({ data: pregnancyRecords, count: pregnancyRecords.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 辅助函数：获取每个母猪耳号的最新记录并按条件过滤（实时计算版本）
async function getLatestRecordsByCondition(condition = {}) {
  const client = await pool.connect();

  try {
    // 使用PostgreSQL的DISTINCT ON获取每个母猪耳号的最新记录
    const query = `
      SELECT DISTINCT ON (pig_ear_number) *
      FROM pig_records
      WHERE pig_ear_number IS NOT NULL AND pig_ear_number != ''
      ORDER BY pig_ear_number, id DESC
    `;

    const result = await client.query(query);
    const allRecords = result.rows;

    // 实时计算天数/状态
    const recordsWithRealTimeStatus = addRealTimeDaysStatus(allRecords);

    // 如果有条件，则过滤
    if (Object.keys(condition).length > 0) {
      return recordsWithRealTimeStatus.filter(record => {
        for (const [key, value] of Object.entries(condition)) {
          if (record[key] !== value) {
            return false;
          }
        }
        return true;
      });
    }

    return recordsWithRealTimeStatus;
  } finally {
    client.release();
  }
}

// 获取分娩列表
app.get('/api/records/birth', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '分娩' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取流产列表
app.get('/api/records/abortion', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '流产' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取空怀列表
app.get('/api/records/empty', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '空怀' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取反情列表
app.get('/api/records/return', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '反情' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取死亡列表
app.get('/api/records/death', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '死亡' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取待妊检列表（天数>21且25天妊检为空）
app.get('/api/records/pregnancy-check', async (req, res) => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT DISTINCT ON (pig_ear_number) *
      FROM pig_records
      WHERE pig_ear_number IS NOT NULL AND pig_ear_number != ''
        AND (pregnancy_check_25 IS NULL OR pregnancy_check_25 = '')
      ORDER BY pig_ear_number, id DESC
    `;

    const result = await client.query(query);
    const allRecords = result.rows;
    client.release();

    // 实时计算天数/状态
    const recordsWithRealTimeStatus = addRealTimeDaysStatus(allRecords);

    // 筛选天数>21的记录
    const pregnancyCheckRecords = recordsWithRealTimeStatus.filter(record => {
      const daysStatus = record.days_status;
      return /^[0-9]+$/.test(daysStatus) && parseInt(daysStatus) > 21;
    });

    // 按天数倒序排列
    pregnancyCheckRecords.sort((a, b) => parseInt(b.days_status) - parseInt(a.days_status));

    res.json({ data: pregnancyCheckRecords, count: pregnancyCheckRecords.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取淘汰列表
app.get('/api/records/eliminate', async (req, res) => {
  try {
    const records = await getLatestRecordsByCondition({ days_status: '淘汰' });
    res.json({ data: records, count: records.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 获取待配种列表（每个母猪耳号最新记录且天数/状态="断奶"）
app.get('/api/records/breeding-ready', async (req, res) => {
  try {
    const client = await pool.connect();

    // 获取每个母猪耳号的最新记录
    const query = `
      SELECT DISTINCT ON (pig_ear_number) *
      FROM pig_records
      WHERE pig_ear_number IS NOT NULL AND pig_ear_number != ''
      ORDER BY pig_ear_number, id DESC
    `;

    const result = await client.query(query);
    const allRecords = result.rows;
    client.release();

    // 实时计算天数/状态
    const recordsWithRealTimeStatus = addRealTimeDaysStatus(allRecords);

    // 筛选天数/状态="断奶"的记录
    const breedingReadyRecords = recordsWithRealTimeStatus.filter(record =>
      record.days_status === '断奶'
    );

    // 按母猪耳号排序
    breedingReadyRecords.sort((a, b) => a.pig_ear_number.localeCompare(b.pig_ear_number));

    console.log(`🐷 待配种列表查询结果: 找到 ${breedingReadyRecords.length} 条记录`);
    console.log(`📅 筛选条件: 每个母猪耳号最新记录且天数/状态="断奶"`);

    res.json({ data: breedingReadyRecords, count: breedingReadyRecords.length });
  } catch (err) {
    console.error('查询失败:', err);
    res.status(500).json({ error: '查询失败' });
  }
});

// 母猪耳号模糊搜索
app.get('/api/records/search', async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.trim() === '') {
      return res.json({ data: [], count: 0, message: '请输入搜索关键词' });
    }

    const searchTerm = q.trim();
    console.log(`🔍 搜索母猪耳号: ${searchTerm}`);

    const client = await pool.connect();

    // 使用PostgreSQL的ILIKE进行模糊搜索（不区分大小写）
    const query = `
      SELECT * FROM pig_records
      WHERE pig_ear_number ILIKE $1
      ORDER BY id DESC
    `;

    const result = await client.query(query, [`%${searchTerm}%`]);
    const records = result.rows;
    client.release();

    // 实时计算天数/状态
    const recordsWithRealTimeStatus = addRealTimeDaysStatus(records);

    console.log(`✅ 找到 ${recordsWithRealTimeStatus.length} 条匹配记录`);
    res.json({
      data: recordsWithRealTimeStatus,
      count: recordsWithRealTimeStatus.length,
      searchTerm: searchTerm,
      message: recordsWithRealTimeStatus.length > 0 ? `找到 ${recordsWithRealTimeStatus.length} 条匹配记录` : '未找到匹配的记录'
    });
  } catch (err) {
    console.error('搜索失败:', err);
    res.status(500).json({ error: '搜索失败: ' + err.message });
  }
});

// 测试天数/状态计算逻辑的API
app.get('/api/test-calculation', (req, res) => {
  const testData = [
    { abnormal_status: '流产', pregnancy_check_25: '正常', breeding_date: '2024/12/1' },
    { abnormal_status: '', pregnancy_check_25: '正常', breeding_date: '2024/12/1' },
    { abnormal_status: '', pregnancy_check_25: '反情', breeding_date: '2024/12/1' },
    { abnormal_status: '', pregnancy_check_25: '', breeding_date: '2024/12/1' },
    { abnormal_status: '', pregnancy_check_25: '正常', breeding_date: '' },
    // 添加当前日期测试用例
    { abnormal_status: '', pregnancy_check_25: '正常', breeding_date: '2025/6/28' },
    { abnormal_status: '', pregnancy_check_25: '正常', breeding_date: '2025/6/29' },
  ];

  const results = testData.map((data, index) => {
    let calculatedDaysStatus = '';
    let calculationLog = '';

    // 第一层逻辑：检查异常状态是否为空
    const abnormalStatus = data.abnormal_status ? String(data.abnormal_status).trim() : '';
    if (abnormalStatus !== '') {
      calculatedDaysStatus = abnormalStatus;
      calculationLog = `第一层逻辑：异常状态不为空，使用异常状态值: ${abnormalStatus}`;
    } else {
      // 第二层逻辑：检查25天妊检的值
      const pregnancyCheck = data.pregnancy_check_25 ? String(data.pregnancy_check_25).trim() : '';
      if (pregnancyCheck === '正常' || pregnancyCheck === '') {
        // 第三层逻辑：检查配种日期是否为空
        if (data.breeding_date && data.breeding_date.trim() !== '') {
          const breedingDate = new Date(data.breeding_date);
          if (!isNaN(breedingDate.getTime())) {
            // 计算从配种日期到今天的天数差（使用北京时间，只比较日期部分）
            const today = new Date();
            const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const breedingDateOnly = new Date(breedingDate.getFullYear(), breedingDate.getMonth(), breedingDate.getDate());
            const timeDiff = todayDate.getTime() - breedingDateOnly.getTime();
            const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

            // 第四层逻辑：根据天数判断分娩和断奶状态
            if (daysDiff > 118 && daysDiff <= 146) {
              calculatedDaysStatus = '分娩';
              calculationLog = `第四层逻辑：天数${daysDiff}天，大于118且小于等于146，判定为分娩`;
            } else if (daysDiff > 146) {
              calculatedDaysStatus = '断奶';
              calculationLog = `第四层逻辑：天数${daysDiff}天，大于146，判定为断奶`;
            } else {
              calculatedDaysStatus = daysDiff.toString();
              calculationLog = `第三层逻辑：25天妊检为"${pregnancyCheck}"，配种日期有效，计算天数差: ${daysDiff}天`;
            }
          } else {
            calculatedDaysStatus = '';
            calculationLog = `第三层逻辑：25天妊检为"${pregnancyCheck}"，但配种日期格式无效`;
          }
        } else {
          calculatedDaysStatus = '';
          calculationLog = `第三层逻辑：25天妊检为"${pregnancyCheck}"，但配种日期为空`;
        }
      } else {
        // 25天妊检不是"正常"或空，返回25天妊检的值
        calculatedDaysStatus = pregnancyCheck;
        calculationLog = `第二层逻辑：25天妊检不是"正常"或空，使用25天妊检值: ${pregnancyCheck}`;
      }
    }

    return {
      testCase: index + 1,
      input: data,
      result: calculatedDaysStatus,
      explanation: calculationLog
    };
  });

  res.json({ results });
});

console.log('📝 开始启动服务器...');

// 启动服务器
app.listen(port, async () => {
  console.log(`🚀 服务器运行在 http://localhost:${port}`);
  console.log('🔗 开始连接PostgreSQL...');
  try {
    await connectPostgreSQL();
    console.log('🎉 系统启动完成！');
  } catch (err) {
    console.error('❌ 系统启动失败:', err);
    console.error('❌ 错误堆栈:', err.stack);

    // 如果PostgreSQL连接失败，回退到内存数据库
    console.log('🔄 回退到内存数据库模式...');
    try {
      // 临时使用内存数据库
      global.memoryDB = [];
      console.log('✅ 内存数据库模式启动成功');
      console.log('🎉 系统启动完成（内存模式）！');
    } catch (memErr) {
      console.error('❌ 内存数据库启动也失败:', memErr);
      process.exit(1);
    }
  }
});

console.log('📝 服务器启动脚本执行完成');
