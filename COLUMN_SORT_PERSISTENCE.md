# 列拖拽排序持久化功能说明

## 🎯 功能概述

为母猪配种查询系统的表格添加了列拖拽排序的持久化功能，用户可以：
- 通过拖拽重新排列表格列的顺序
- 自定义的列顺序会自动保存到浏览器本地存储
- 下次访问时自动恢复上次的列顺序设置
- 可以一键重置为默认列顺序

## 🔧 技术实现

### 1. **本地存储机制**
```javascript
// 存储键名
const COLUMN_ORDER_KEY = 'pig_breeding_column_order';

// 默认列顺序
const DEFAULT_COLUMNS = [
    '母猪耳号', '配时状态', '胎次', '天数/状态', '配种日期', 
    '首配公猪', '次配公猪', '首配人', '次配人', '25天妊检', 
    '异常状态', '异常日期', '预产日期', '栋舍', '栏位', '备注'
];
```

### 2. **核心功能函数**

#### 保存列顺序
```javascript
function saveColumnOrder() {
    try {
        const headers = Array.from(thead.querySelectorAll('th')).map(th => th.textContent.trim());
        localStorage.setItem(COLUMN_ORDER_KEY, JSON.stringify(headers));
        updateColumnOrderStatus();
    } catch (error) {
        console.warn('保存列顺序失败:', error);
    }
}
```

#### 加载列顺序
```javascript
function loadColumnOrder() {
    try {
        const saved = localStorage.getItem(COLUMN_ORDER_KEY);
        if (saved) {
            return JSON.parse(saved);
        }
    } catch (error) {
        console.warn('加载列顺序失败:', error);
    }
    return DEFAULT_COLUMNS;
}
```

#### 应用列顺序
```javascript
function applyColumnOrder(targetOrder) {
    // 创建新的列顺序映射
    const orderMap = new Map();
    targetOrder.forEach((colName, newIndex) => {
        const oldIndex = currentOrder.indexOf(colName);
        if (oldIndex !== -1) {
            orderMap.set(oldIndex, newIndex);
        }
    });

    // 应用新顺序到所有行
    for (const row of table.rows) {
        // 重新排列单元格...
    }
}
```

### 3. **自动触发机制**

#### 拖拽完成时自动保存
```javascript
function handleDrop(e) {
    // ... 拖拽处理逻辑 ...
    
    // 执行列重排
    reorderTableColumns(draggingColumnIndex, targetIndex);
    
    // 自动保存新的列顺序
    saveColumnOrder();
}
```

#### 表格加载时自动应用
```javascript
const observer = new MutationObserver((mutationsList) => {
    for(const mutation of mutationsList) {
        if (mutation.type === 'childList' && thead.querySelector('th')) {
            setTimeout(() => {
                const savedOrder = loadColumnOrder();
                applyColumnOrder(savedOrder);
                enableColumnDragging();
                updateColumnOrderStatus();
            }, 100);
        }
    }
});
```

## 🎨 用户界面

### 1. **重置按钮**
- 位置：搜索栏右侧
- 功能：一键重置列顺序为默认
- 样式：灰色按钮，悬停时变深
- 反馈：点击后显示"✅ 已重置"2秒

### 2. **状态指示器**
- 显示条件：当前使用自定义列顺序时
- 内容：📋 当前使用自定义列顺序
- 样式：蓝色背景，淡入动画

### 3. **视觉反馈**
- 拖拽指示器：列标题左侧显示"⋮⋮"
- 悬停效果：背景色变化
- 拖拽时：半透明 + 旋转效果
- 目标位置：高亮边框 + 缩放效果

## 📱 使用方法

### 1. **拖拽排序**
1. 将鼠标悬停在要移动的列标题上
2. 看到拖拽指示器"⋮⋮"后，按住鼠标左键
3. 拖拽到目标位置（会看到高亮提示）
4. 释放鼠标完成移动
5. 新顺序自动保存

### 2. **重置顺序**
- **方法一**：点击"🔄 重置列顺序"按钮
- **方法二**：使用快捷键 `Ctrl + R`
- 确认对话框后执行重置

### 3. **查看状态**
- 如果使用了自定义顺序，会在搜索栏下方显示状态提示
- 重置后状态提示自动隐藏

## 🔍 数据存储格式

### localStorage 存储结构
```json
{
  "pig_breeding_column_order": [
    "母猪耳号",
    "配种日期", 
    "配时状态",
    "胎次",
    "天数/状态",
    "首配公猪",
    "次配公猪",
    "首配人",
    "次配人",
    "25天妊检",
    "异常状态",
    "异常日期",
    "预产日期",
    "栋舍",
    "栏位",
    "备注"
  ]
}
```

## ⚡ 性能优化

### 1. **事件委托**
- 使用事件委托避免为每个th元素绑定事件
- 减少内存占用和提高性能

### 2. **延迟执行**
- 使用setTimeout确保DOM完全更新后再执行
- 避免在DOM变化过程中操作

### 3. **智能重绑定**
- 使用标志位防止重复绑定事件
- 只在必要时重新设置draggable属性

### 4. **错误处理**
- 所有localStorage操作都有try-catch保护
- 失败时回退到默认行为

## 🛡️ 兼容性和容错

### 1. **浏览器兼容性**
- 支持localStorage的现代浏览器
- 不支持时自动降级为非持久化模式

### 2. **数据容错**
- localStorage数据损坏时自动使用默认顺序
- JSON解析失败时有错误处理

### 3. **DOM容错**
- 表格结构变化时自动重新初始化
- 缺少元素时跳过相关功能

## 🔧 维护和扩展

### 1. **添加新列**
更新DEFAULT_COLUMNS数组：
```javascript
const DEFAULT_COLUMNS = [
    // ... 现有列 ...
    '新列名'
];
```

### 2. **修改存储键名**
```javascript
const COLUMN_ORDER_KEY = 'new_storage_key';
```

### 3. **自定义重置逻辑**
```javascript
function resetColumnOrder() {
    // 自定义重置逻辑
    localStorage.removeItem(COLUMN_ORDER_KEY);
    applyColumnOrder(CUSTOM_DEFAULT_ORDER);
    updateColumnOrderStatus();
}
```

## 📊 使用统计

可以添加使用统计功能：
```javascript
function trackColumnReorder() {
    const stats = JSON.parse(localStorage.getItem('column_reorder_stats') || '{}');
    stats.count = (stats.count || 0) + 1;
    stats.lastUsed = new Date().toISOString();
    localStorage.setItem('column_reorder_stats', JSON.stringify(stats));
}
```

## ⚠️ 注意事项

1. **数据同步**：localStorage是浏览器本地存储，不会在不同设备间同步
2. **存储限制**：localStorage有大小限制（通常5-10MB）
3. **隐私模式**：某些浏览器的隐私模式可能限制localStorage
4. **清理数据**：用户清理浏览器数据时会丢失自定义设置
5. **版本兼容**：列结构变化时需要考虑向后兼容性
