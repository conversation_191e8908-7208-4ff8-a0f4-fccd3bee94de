# 列拖拽排序持久化功能测试指南

## 🧪 测试环境
- 浏览器：Chrome/Firefox/Edge（支持localStorage）
- 地址：http://localhost:3000
- 测试数据：需要有表格数据显示

## 📋 测试步骤

### 1. **基础拖拽功能测试**

#### 测试1.1：拖拽指示器显示
- [ ] 将鼠标悬停在任意列标题上
- [ ] 验证左侧是否显示"⋮⋮"拖拽指示器
- [ ] 验证鼠标光标是否变为移动样式

#### 测试1.2：拖拽操作
- [ ] 拖拽"母猪耳号"列到"配种日期"列之后
- [ ] 验证拖拽过程中列标题是否半透明并旋转
- [ ] 验证目标位置是否有蓝色高亮边框
- [ ] 验证释放后列顺序是否正确改变
- [ ] 验证数据行是否跟随列标题一起移动

### 2. **持久化存储测试**

#### 测试2.1：保存功能
- [ ] 执行一次列拖拽操作
- [ ] 打开浏览器开发者工具 (F12)
- [ ] 进入 Application/Storage → Local Storage → http://localhost:3000
- [ ] 验证是否存在 `pig_breeding_column_order` 键
- [ ] 验证值是否为正确的JSON数组格式

#### 测试2.2：加载功能
- [ ] 刷新页面 (F5)
- [ ] 验证表格列顺序是否保持上次的自定义顺序
- [ ] 验证状态指示器是否显示"📋 当前使用自定义列顺序"

#### 测试2.3：跨会话持久化
- [ ] 关闭浏览器标签页
- [ ] 重新打开 http://localhost:3000
- [ ] 验证自定义列顺序是否仍然保持

### 3. **重置功能测试**

#### 测试3.1：按钮重置
- [ ] 点击"🔄 重置列顺序"按钮
- [ ] 验证是否弹出确认对话框
- [ ] 点击"确定"
- [ ] 验证列顺序是否恢复为默认顺序
- [ ] 验证按钮是否短暂显示"✅ 已重置"
- [ ] 验证状态指示器是否隐藏

#### 测试3.2：快捷键重置
- [ ] 先进行一次拖拽操作
- [ ] 按下 Ctrl + R
- [ ] 验证是否弹出确认对话框（而不是刷新页面）
- [ ] 确认后验证是否重置成功

### 4. **边界情况测试**

#### 测试4.1：拖拽到边界位置
- [ ] 拖拽列到第一个位置
- [ ] 拖拽列到最后一个位置
- [ ] 验证操作是否正常，数据是否正确

#### 测试4.2：拖拽到自身位置
- [ ] 拖拽列到自身位置
- [ ] 验证是否无变化（不应该触发保存）

#### 测试4.3：多次拖拽
- [ ] 连续进行多次拖拽操作
- [ ] 验证每次操作后localStorage是否正确更新
- [ ] 验证最终顺序是否正确

### 5. **状态指示器测试**

#### 测试5.1：显示逻辑
- [ ] 默认状态下状态指示器应该隐藏
- [ ] 进行拖拽后状态指示器应该显示
- [ ] 重置后状态指示器应该隐藏

#### 测试5.2：动画效果
- [ ] 验证状态指示器出现时是否有淡入动画
- [ ] 验证样式是否正确（蓝色背景，左边框）

### 6. **数据完整性测试**

#### 测试6.1：数据对应关系
- [ ] 记录拖拽前某行的完整数据
- [ ] 执行列拖拽操作
- [ ] 验证该行数据是否完整且对应关系正确

#### 测试6.2：搜索功能兼容性
- [ ] 自定义列顺序后进行搜索
- [ ] 验证搜索结果的列顺序是否保持自定义顺序
- [ ] 验证搜索结果数据是否正确对应

### 7. **错误处理测试**

#### 测试7.1：localStorage禁用
- [ ] 在浏览器中禁用localStorage（隐私模式或设置）
- [ ] 验证拖拽功能是否仍然可用（非持久化模式）
- [ ] 验证是否有错误提示或控制台错误

#### 测试7.2：数据损坏处理
- [ ] 在开发者工具中手动修改localStorage数据为无效JSON
- [ ] 刷新页面
- [ ] 验证是否自动回退到默认列顺序

## 🔍 验证要点

### localStorage数据格式验证
正确的存储格式应该是：
```json
[
  "母猪耳号",
  "配种日期",
  "配时状态",
  "胎次",
  "天数/状态",
  "首配公猪",
  "次配公猪",
  "首配人",
  "次配人",
  "25天妊检",
  "异常状态",
  "异常日期",
  "预产日期",
  "栋舍",
  "栏位",
  "备注"
]
```

### 默认列顺序
```
母猪耳号 → 配时状态 → 胎次 → 天数/状态 → 配种日期 → 首配公猪 → 次配公猪 → 首配人 → 次配人 → 25天妊检 → 异常状态 → 异常日期 → 预产日期 → 栋舍 → 栏位 → 备注
```

## 🐛 常见问题排查

### 问题1：拖拽无响应
- 检查表格是否已加载完成
- 检查控制台是否有JavaScript错误
- 验证th元素是否有draggable="true"属性

### 问题2：持久化不生效
- 检查localStorage是否被禁用
- 检查控制台是否有存储相关错误
- 验证localStorage中的数据格式

### 问题3：重置功能无效
- 检查重置按钮是否正确绑定事件
- 验证localStorage数据是否被正确清除
- 检查applyColumnOrder函数是否正常执行

### 问题4：状态指示器不显示
- 检查DOM元素是否存在
- 验证updateColumnOrderStatus函数逻辑
- 检查CSS样式是否正确应用

## 📊 性能测试

### 内存使用
- [ ] 多次拖拽操作后检查内存使用情况
- [ ] 验证是否有内存泄漏

### 响应速度
- [ ] 大量数据时的拖拽响应速度
- [ ] 页面加载时应用列顺序的速度

## ✅ 测试完成标准

所有测试项目通过后，功能被认为是稳定可用的：
- [ ] 基础拖拽功能正常
- [ ] 持久化存储和加载正常
- [ ] 重置功能正常
- [ ] 状态指示器正常
- [ ] 数据完整性保持
- [ ] 错误处理正常
- [ ] 性能表现良好

## 📝 测试报告模板

```
测试日期：____
测试浏览器：____
测试人员：____

基础功能：✅/❌
持久化功能：✅/❌
重置功能：✅/❌
边界情况：✅/❌
错误处理：✅/❌

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
