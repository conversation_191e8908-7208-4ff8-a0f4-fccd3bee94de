// 菜单配置
const menuItems = [
    { id: 'all', title: '总记录', endpoint: '/api/records/all' },
    { id: 'statistics', title: '妊娠中', endpoint: '/api/records/statistics' },
    { id: 'birth', title: '分娩', endpoint: '/api/records/birth' },
    { id: 'abortion', title: '流产', endpoint: '/api/records/abortion' },
    { id: 'empty', title: '空怀', endpoint: '/api/records/empty' },
    { id: 'return', title: '反情', endpoint: '/api/records/return' },
    { id: 'death', title: '死亡', endpoint: '/api/records/death' },
    { id: 'pregnancy-check', title: '待妊检', endpoint: '/api/records/pregnancy-check' },
    { id: 'eliminate', title: '淘汰', endpoint: '/api/records/eliminate' },
    { id: 'breeding-ready', title: '待配种', endpoint: '/api/records/breeding-ready' }
];

// 表格列配置
const tableColumns = [
    { key: 'pig_ear_number', title: '母猪耳号' },
    { key: 'breeding_status', title: '配时状态' },
    { key: 'parity', title: '胎次' },
    { key: 'days_status', title: '天数/状态' },
    { key: 'breeding_date', title: '配种日期' },
    { key: 'first_boar', title: '首配公猪' },
    { key: 'second_boar', title: '次配公猪' },
    { key: 'first_person', title: '首配人' },
    { key: 'second_person', title: '次配人' },
    { key: 'pregnancy_check_25', title: '25天妊检' },
    { key: 'abnormal_status', title: '异常状态' },
    { key: 'abnormal_date', title: '异常日期' },
    { key: 'expected_date', title: '预产日期' },
    { key: 'building', title: '栋舍' },
    { key: 'pen', title: '栏位' },
    { key: 'notes', title: '备注' }
];

// DOM元素
const excelFileInput = document.getElementById('excelFile');
const uploadBtn = document.getElementById('uploadBtn');
const downloadTemplateBtn = document.getElementById('downloadTemplateBtn');
const uploadMessage = document.getElementById('uploadMessage');
const dataSection = document.getElementById('dataSection');
const backBtn = document.getElementById('backBtn');
const dataTitle = document.getElementById('dataTitle');
const dataCount = document.getElementById('dataCount');
const tableHead = document.getElementById('tableHead');
const tableBody = document.getElementById('tableBody');

// 搜索相关DOM元素
const searchInput = document.getElementById('searchInput');
const searchMessage = document.getElementById('searchMessage');
const menuSelect = document.getElementById('menuSelect');

// 所有记录相关DOM元素
const allRecordsCount = document.getElementById('allRecordsCount');
const allRecordsTableHead = document.getElementById('allRecordsTableHead');
const allRecordsTableBody = document.getElementById('allRecordsTableBody');

// 模态框相关DOM元素
const recordModal = document.getElementById('recordModal');
const modalClose = document.getElementById('modalClose');
const modalBody = document.getElementById('modalBody');

// 分页相关DOM元素
const pagination = document.getElementById('pagination');
const paginationInfo = document.getElementById('paginationInfo');
const firstPageBtn = document.getElementById('firstPageBtn');
const prevPageBtn = document.getElementById('prevPageBtn');
const nextPageBtn = document.getElementById('nextPageBtn');
const lastPageBtn = document.getElementById('lastPageBtn');
const pageNumbers = document.getElementById('pageNumbers');
const pageSizeSelect = document.getElementById('pageSizeSelect');

// 当前活动的菜单项
let currentActiveMenuItem = null;

// 分页相关变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 0;
let allData = []; // 存储所有数据

// 自动刷新功能 - 每小时检查一次是否需要更新数据
let lastRefreshDate = new Date().toDateString();
let autoRefreshInterval;

function startAutoRefresh() {
    // 每小时检查一次日期是否变化
    autoRefreshInterval = setInterval(() => {
        const currentDate = new Date().toDateString();
        if (currentDate !== lastRefreshDate) {
            console.log('📅 检测到日期变化，自动刷新数据...');
            lastRefreshDate = currentDate;

            // 刷新菜单统计数据
            loadMenuCounts();

            // 如果当前显示的是特定页面，自动刷新
            const selectedValue = menuSelect.value;
            if (selectedValue) {
                console.log('🔄 自动刷新当前页面数据');
                const selectedOption = menuSelect.querySelector(`option[value="${selectedValue}"]`);
                if (selectedOption) {
                    const endpoint = selectedOption.dataset.endpoint;
                    loadData(endpoint, selectedOption.textContent);
                }
            } else {
                // 刷新总列表
                loadAllRecords();
            }
        }
    }, 3600000); // 每小时检查一次 (3600000ms = 1小时)
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeMenu();
    setupEventListeners();
    loadMenuCounts();
    loadAllRecords(); // 加载所有记录

    // 启动自动刷新
    startAutoRefresh();
    console.log('⏰ 自动刷新已启动，每小时检查日期变化');
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});

// 设置事件监听器
function setupEventListeners() {
    excelFileInput.addEventListener('change', function() {
        uploadBtn.disabled = !this.files.length;
    });

    uploadBtn.addEventListener('click', uploadFile);
    downloadTemplateBtn.addEventListener('click', downloadTemplate);
    backBtn.addEventListener('click', showMenu);

    // 搜索功能事件监听器 - 自动搜索
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.trim();

        // 清除之前的定时器
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (searchTerm === '') {
            // 如果搜索框为空，恢复到默认状态
            clearSearchAndRestoreDefault();
        } else {
            // 延迟搜索，避免频繁请求
            searchTimeout = setTimeout(() => {
                performAutoSearch(searchTerm);
            }, 300); // 300ms延迟
        }
    });

    // 下拉菜单事件监听器
    menuSelect.addEventListener('change', function() {
        const selectedValue = this.value;
        if (selectedValue) {
            // 清空搜索框
            searchInput.value = '';
            showSearchMessage('', '');

            // 加载选中的数据类型
            const selectedOption = this.options[this.selectedIndex];
            const endpoint = selectedOption.dataset.endpoint;

            loadDataByEndpoint(endpoint);
        } else {
            // 如果选择了空选项，显示所有记录
            loadAllRecords();
        }
    });

    // 模态框事件监听器
    modalClose.addEventListener('click', closeModal);
    recordModal.addEventListener('click', function(e) {
        if (e.target === recordModal) {
            closeModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && recordModal.classList.contains('show')) {
            closeModal();
        }
    });

    // 分页事件监听器
    firstPageBtn.addEventListener('click', () => goToPage(1));
    prevPageBtn.addEventListener('click', () => goToPage(currentPage - 1));
    nextPageBtn.addEventListener('click', () => goToPage(currentPage + 1));
    lastPageBtn.addEventListener('click', () => goToPage(totalPages));

    pageSizeSelect.addEventListener('change', function() {
        pageSize = parseInt(this.value);
        currentPage = 1;
        renderCurrentPage();
    });
}

// 初始化菜单下拉列表
function initializeMenu() {
    menuSelect.innerHTML = '<option value="">选择查询类型</option>';
    menuItems.forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = item.title;
        option.dataset.endpoint = item.endpoint;
        menuSelect.appendChild(option);
    });
}

// 加载菜单数量
async function loadMenuCounts() {
    for (const item of menuItems) {
        try {
            const response = await fetch(item.endpoint);
            const data = await response.json();
            // 更新下拉选项文本，显示数量
            const option = menuSelect.querySelector(`option[value="${item.id}"]`);
            if (option) {
                option.textContent = `${item.title} (${data.count || 0})`;
            }
        } catch (error) {
            console.error(`加载${item.title}数量失败:`, error);
            const option = menuSelect.querySelector(`option[value="${item.id}"]`);
            if (option) {
                option.textContent = `${item.title} (-)`;
            }
        }
    }
}

// 上传文件
async function uploadFile() {
    const file = excelFileInput.files[0];
    if (!file) {
        showMessage('请选择Excel文件', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('excel', file);
    
    uploadBtn.disabled = true;
    uploadBtn.textContent = '上传中...';
    showMessage('正在上传和处理数据，请稍候...', 'loading');
    
    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showMessage(`数据导入成功！共导入 ${result.count} 条记录`, 'success');
            excelFileInput.value = '';
            loadMenuCounts(); // 重新加载数量
            loadAllRecords(); // 重新加载所有记录
        } else {
            showMessage(`导入失败: ${result.error}`, 'error');
        }
    } catch (error) {
        showMessage(`上传失败: ${error.message}`, 'error');
    } finally {
        uploadBtn.disabled = false;
        uploadBtn.textContent = '上传并导入数据';
    }
}

// 显示消息
function showMessage(message, type) {
    uploadMessage.innerHTML = `<div class="${type}">${message}</div>`;
    if (type !== 'loading') {
        setTimeout(() => {
            uploadMessage.innerHTML = '';
        }, 5000);
    }
}

// 加载数据
async function loadData(menuItem) {
    dataTitle.textContent = menuItem.title;
    dataCount.textContent = '加载中...';
    tableBody.innerHTML = '<tr><td colspan="16" class="loading">正在加载数据...</td></tr>';
    
    showData();
    
    try {
        const response = await fetch(menuItem.endpoint);
        const result = await response.json();
        
        if (response.ok) {
            dataCount.textContent = `共 ${result.count} 条记录`;
            renderTable(result.data);
        } else {
            tableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${result.error}</td></tr>`;
            dataCount.textContent = '0';
        }
    } catch (error) {
        tableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${error.message}</td></tr>`;
        dataCount.textContent = '0';
    }
}

// 渲染表格
function renderTable(data) {
    // 渲染表头
    const headerRow = document.createElement('tr');
    tableColumns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column.title;
        headerRow.appendChild(th);
    });
    tableHead.innerHTML = '';
    tableHead.appendChild(headerRow);
    
    // 渲染数据
    tableBody.innerHTML = '';
    if (data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="16" style="text-align: center; padding: 20px;">暂无数据</td></tr>';
        return;
    }
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.classList.add('data-row');

        // 添加点击事件
        tr.addEventListener('click', function() {
            showRecordModal(row);
        });

        tableColumns.forEach(column => {
            const td = document.createElement('td');
            let value = row[column.key];

            // 格式化日期
            if (column.key.includes('date') && value) {
                value = new Date(value).toLocaleDateString('zh-CN');
            }

            td.textContent = value || '';
            tr.appendChild(td);
        });
        tableBody.appendChild(tr);
    });
}

// 显示数据页面
function showData() {
    menuSection.style.display = 'none';
    dataSection.classList.add('active');
}

// 下载示例模板文件
function downloadTemplate() {
    // 创建示例数据 - 完全按照用户真实Excel结构A-P列 (天数/状态将自动计算，D列可留空或填写原始值)
    const templateData = [
        ['母猪耳号', '配时状态', '胎次', '天数/状态', '配种日期', '首配公猪', '次配公猪', '首配人', '次配人', '25天妊检', '异常状态', '异常日期', '预产日期', '栋舍', '栏位', '备注'],
        ['TEST001', '人流', '1', '', '2024/12/1', 'DD102256', 'DD102256', '张三', '张三', '正常', '', '', '', '产1', '1-1', '测试正常情况'],
        ['TEST002', '人流', '2', '', '2024/11/15', 'DD102257', 'DD102257', '李四', '李四', '', '流产', '2024/12/20', '', '产2', '2-1', '测试异常状态优先'],
        ['TEST003', '人流', '1', '', '2024/12/10', 'DD102258', 'DD102258', '王五', '王五', '反情', '', '', '', '产3', '3-1', '测试25天妊检非正常'],
        ['TEST004', '人流', '3', '', '', 'DD102259', 'DD102259', '赵六', '赵六', '正常', '', '', '', '产4', '4-1', '测试配种日期为空'],
        ['TEST005', '人流', '2', '', '2024/7/1', 'DD102260', 'DD102260', '孙七', '孙七', '正常', '', '', '', '产5', '5-1', '测试分娩状态'],
        ['TEST006', '人流', '1', '', '2024/4/1', 'DD102261', 'DD102261', '钱八', '钱八', '正常', '', '', '', '产6', '6-1', '测试断奶状态'],
        ['LY23115', '人流', '2', '', '2024/1/2', 'DD102256', 'DD102257', '张仁富', '张仁富', '正常', '', '', '', '产6', '1-4', ''],
        ['LY23164', '人流', '2', '', '2024/1/2', 'DD102256', 'DD102257', '张仁富', '张仁富', '正常', '淘汰', '2024/5/22', '', '产6', '1-5', ''],
        ['LY23113', '人流', '2', '', '2024/1/2', 'DD102256', 'DD102257', '张仁富', '张仁富', '正常', '反情', '2024/1/21', '', '产6', '2-9', ''],
        ['LY23215', '人流', '2', '', '2024/1/2', 'DD102256', 'DD102257', '张仁富', '张仁富', '', '反情', '2024/1/21', '', '产6', '2-8', '']
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(templateData);

    // 设置列宽 (16列A-P，完全按照用户真实Excel结构)
    const colWidths = [
        {wch: 12}, {wch: 10}, {wch: 8}, {wch: 12}, {wch: 12},  // A-E: 母猪耳号,配时状态,胎次,天数/状态,配种日期
        {wch: 12}, {wch: 12}, {wch: 10}, {wch: 10}, {wch: 12}, // F-J: 首配公猪,次配公猪,首配人,次配人,25天妊检
        {wch: 10}, {wch: 12}, {wch: 12}, {wch: 8}, {wch: 8}, {wch: 15}  // K-P: 异常状态,异常日期,预产日期,栋舍,栏位,备注
    ];
    ws['!cols'] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '配种记录');

    // 下载文件
    XLSX.writeFile(wb, '母猪配种记录模板.xlsx');

    showMessage('示例文件下载成功！请查看下载文件夹', 'success');
}

// 显示数据页面
function showData() {
    menuSection.style.display = 'none';
    dataSection.classList.add('active');
}

// 显示菜单页面
function showMenu() {
    dataSection.classList.remove('active');
    menuSection.style.display = 'block';
}

// 执行自动搜索
async function performAutoSearch(searchTerm) {
    // 清除菜单选中状态
    if (currentActiveMenuItem) {
        currentActiveMenuItem.classList.remove('active');
        currentActiveMenuItem = null;
    }

    // 显示搜索状态
    showSearchMessage('正在搜索...', 'loading');

    // 更新主列表状态
    allRecordsCount.textContent = '搜索中...';
    allRecordsTableBody.innerHTML = '<tr><td colspan="16" class="loading">正在搜索...</td></tr>';

    try {
        const response = await fetch(`/api/records/search?q=${encodeURIComponent(searchTerm)}`);
        const result = await response.json();

        if (response.ok) {
            showSearchMessage(result.message, result.count > 0 ? 'success' : 'info');
            allRecordsCount.textContent = `共 ${result.count} 条记录`;
            initializePagination(result.data);
        } else {
            showSearchMessage(`搜索失败: ${result.error}`, 'error');
            allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">搜索失败: ${result.error}</td></tr>`;
            allRecordsCount.textContent = '搜索失败';
        }
    } catch (error) {
        showSearchMessage(`搜索失败: ${error.message}`, 'error');
        allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">搜索失败: ${error.message}</td></tr>`;
        allRecordsCount.textContent = '搜索失败';
    }
}

// 清空搜索并恢复默认状态
function clearSearchAndRestoreDefault() {
    showSearchMessage('', '');

    // 检查下拉菜单是否有选择
    if (menuSelect.value) {
        // 如果有选择，重新加载选中的数据
        const selectedOption = menuSelect.options[menuSelect.selectedIndex];
        const endpoint = selectedOption.dataset.endpoint;
        loadDataByEndpoint(endpoint);
    } else {
        // 如果没有选择，恢复显示所有记录
        loadAllRecords();
    }
}

// 显示搜索消息
function showSearchMessage(message, type) {
    if (!message) {
        searchMessage.innerHTML = '';
        return;
    }

    const className = type === 'error' ? 'error' :
                     type === 'success' ? 'success' :
                     type === 'loading' ? 'loading' : 'info';
    searchMessage.innerHTML = `<div class="${className}">${message}</div>`;

    if (type !== 'loading' && type !== '') {
        setTimeout(() => {
            if (type === 'error') {
                searchMessage.innerHTML = '';
            }
        }, 5000);
    }
}



// 通过API端点加载数据
async function loadDataByEndpoint(endpoint) {
    // 加载数据
    allRecordsCount.textContent = '加载中...';
    allRecordsTableBody.innerHTML = '<tr><td colspan="16" class="loading">正在加载数据...</td></tr>';

    try {
        const response = await fetch(endpoint);
        const result = await response.json();

        if (response.ok) {
            allRecordsCount.textContent = `共 ${result.count} 条记录`;
            initializePagination(result.data);
        } else {
            allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${result.error}</td></tr>`;
            allRecordsCount.textContent = '加载失败';
        }
    } catch (error) {
        allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${error.message}</td></tr>`;
        allRecordsCount.textContent = '加载失败';
    }
}

// 加载所有记录（初始化时使用）
async function loadAllRecords() {
    // 重置下拉菜单
    menuSelect.value = '';

    // 加载数据
    allRecordsCount.textContent = '加载中...';
    allRecordsTableBody.innerHTML = '<tr><td colspan="16" class="loading">正在加载所有记录...</td></tr>';

    try {
        const response = await fetch('/api/records/all');
        const result = await response.json();

        if (response.ok) {
            allRecordsCount.textContent = `共 ${result.count} 条记录`;
            initializePagination(result.data);
        } else {
            allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${result.error}</td></tr>`;
            allRecordsCount.textContent = '加载失败';
        }
    } catch (error) {
        allRecordsTableBody.innerHTML = `<tr><td colspan="16" class="error">加载失败: ${error.message}</td></tr>`;
        allRecordsCount.textContent = '加载失败';
    }
}

// 渲染所有记录表格
function renderAllRecordsTable(data) {
    // 渲染表头
    const headerRow = document.createElement('tr');
    tableColumns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column.title;
        headerRow.appendChild(th);
    });
    allRecordsTableHead.innerHTML = '';
    allRecordsTableHead.appendChild(headerRow);

    // 渲染数据
    allRecordsTableBody.innerHTML = '';
    if (data.length === 0) {
        allRecordsTableBody.innerHTML = '<tr><td colspan="16" style="text-align: center; padding: 20px;">暂无记录</td></tr>';
        return;
    }

    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.classList.add('data-row');

        // 添加点击事件
        tr.addEventListener('click', function() {
            showRecordModal(row);
        });

        tableColumns.forEach(column => {
            const td = document.createElement('td');
            let value = row[column.key];

            // 格式化日期
            if (column.key.includes('date') && value) {
                value = new Date(value).toLocaleDateString('zh-CN');
            }

            td.textContent = value || '';
            tr.appendChild(td);
        });
        allRecordsTableBody.appendChild(tr);
    });
}

// 显示记录详情模态框
function showRecordModal(record) {
    const modalTitle = document.querySelector('.modal-title');
    modalTitle.textContent = `记录详情 - ${record.pig_ear_number || '未知'}`;

    // 生成详细信息HTML
    let detailsHTML = '';
    tableColumns.forEach(column => {
        let value = record[column.key];

        // 格式化日期
        if (column.key.includes('date') && value) {
            value = new Date(value).toLocaleDateString('zh-CN');
        }

        // 处理空值
        const displayValue = value || '';
        const isEmpty = !displayValue;

        detailsHTML += `
            <div class="detail-item">
                <span class="detail-label">${column.title}:</span>
                <span class="detail-value ${isEmpty ? 'empty' : ''}">${isEmpty ? '暂无数据' : displayValue}</span>
                ${column.key === 'days_status' && record.days_status_original && record.days_status_original !== record.days_status ?
                    `<small style="color: #666; margin-left: 10px;">(原值: ${record.days_status_original})</small>` : ''}
            </div>
        `;


    });

    modalBody.innerHTML = detailsHTML;
    recordModal.classList.add('show');

    // 防止背景滚动
    document.body.style.overflow = 'hidden';
}

// 关闭模态框
function closeModal() {
    recordModal.classList.remove('show');
    document.body.style.overflow = '';
}

// 分页相关函数
function initializePagination(data) {
    allData = data;
    totalRecords = data.length;
    totalPages = Math.ceil(totalRecords / pageSize);
    currentPage = 1;

    if (totalRecords > pageSize) {
        pagination.style.display = 'flex';
        renderCurrentPage();
    } else {
        pagination.style.display = 'none';
        renderAllRecordsTable(data);
    }
}

function renderCurrentPage() {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, totalRecords);
    const currentData = allData.slice(startIndex, endIndex);

    renderAllRecordsTable(currentData);
    updatePaginationInfo();
    updatePaginationButtons();
}

function updatePaginationInfo() {
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, totalRecords);
    paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${totalRecords} 条记录`;
}

function updatePaginationButtons() {
    // 更新按钮状态
    firstPageBtn.disabled = currentPage === 1;
    prevPageBtn.disabled = currentPage === 1;
    nextPageBtn.disabled = currentPage === totalPages;
    lastPageBtn.disabled = currentPage === totalPages;

    // 生成页码按钮
    renderPageNumbers();
}

function renderPageNumbers() {
    pageNumbers.innerHTML = '';

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // 确保显示5个页码（如果可能）
    if (endPage - startPage < 4) {
        if (startPage === 1) {
            endPage = Math.min(totalPages, startPage + 4);
        } else {
            startPage = Math.max(1, endPage - 4);
        }
    }

    // 如果起始页不是1，显示省略号
    if (startPage > 1) {
        const btn = createPageButton(1);
        pageNumbers.appendChild(btn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            pageNumbers.appendChild(ellipsis);
        }
    }

    // 显示页码按钮
    for (let i = startPage; i <= endPage; i++) {
        const btn = createPageButton(i);
        pageNumbers.appendChild(btn);
    }

    // 如果结束页不是最后一页，显示省略号
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            pageNumbers.appendChild(ellipsis);
        }
        const btn = createPageButton(totalPages);
        pageNumbers.appendChild(btn);
    }
}

function createPageButton(pageNum) {
    const btn = document.createElement('button');
    btn.textContent = pageNum;
    btn.className = 'pagination-btn';
    if (pageNum === currentPage) {
        btn.classList.add('active');
    }
    btn.addEventListener('click', () => goToPage(pageNum));
    return btn;
}

function goToPage(pageNum) {
    if (pageNum < 1 || pageNum > totalPages || pageNum === currentPage) {
        return;
    }
    currentPage = pageNum;
    renderCurrentPage();
}


document.addEventListener('DOMContentLoaded', function() {
    // ... 您现有的其他 app.js 代码 ...

    // --- 开始添加拖拽排序列的功能 ---

    const table = document.getElementById('allRecordsTable');
    const thead = document.getElementById('allRecordsTableHead');

    // 确保在表头加载完毕后执行
    // 如果您的表头是动态生成的，请确保在生成后再调用此函数
    function enableColumnDragging() {
        let draggingElement; // 用来存储正在被拖拽的th元素
        let draggingColumnIndex; // 存储被拖拽列的起始索引

        // 为所有表头th元素绑定事件
        thead.querySelectorAll('th').forEach((header, index) => {
            header.setAttribute('draggable', true); // 设置为可拖拽

            // 拖拽开始
            header.addEventListener('dragstart', (e) => {
                draggingElement = e.target;
                draggingColumnIndex = index;
                // 设置拖拽效果
                e.dataTransfer.effectAllowed = 'move';
                draggingElement.style.opacity = '0.5'; // 设置拖拽时半透明
            });

            // 当拖拽元素经过其他可放置区域时
            header.addEventListener('dragover', (e) => {
                e.preventDefault(); // 必须阻止默认事件，否则drop事件不会触发
                if (e.target !== draggingElement) {
                    e.target.classList.add('drag-over');
                }
            });

            // 当拖拽元素离开可放置区域时
            header.addEventListener('dragleave', (e) => {
                e.target.classList.remove('drag-over');
            });

            // 当拖拽元素被放置时
            header.addEventListener('drop', (e) => {
                e.preventDefault();
                e.target.classList.remove('drag-over');

                // 获取目标列的索引
                const targetHeader = e.target.closest('th'); // 确保获取到的是th元素
                const targetIndex = Array.from(thead.querySelectorAll('th')).indexOf(targetHeader);

                // 如果拖拽到非th元素或者自身，则不执行
                if (targetIndex === -1 || draggingColumnIndex === targetIndex) {
                    return;
                }
                
                // 执行列重排
                reorderTableColumns(draggingColumnIndex, targetIndex);

                // drop事件后需要重新获取表头并重新绑定事件，因为DOM结构改变了
                enableColumnDragging();
            });
            
            // 拖拽结束
            header.addEventListener('dragend', (e) => {
                 e.target.style.opacity = '1'; // 恢复不透明
                 // 移除所有可能残留的drag-over样式
                 thead.querySelectorAll('th').forEach(th => th.classList.remove('drag-over'));
            });
        });
    }

    /**
     * 重排表格的列
     * @param {number} fromIndex - 起始列索引
     * @param {number} toIndex - 目标列索引
     */
    function reorderTableColumns(fromIndex, toIndex) {
        // 遍历表格的每一行 (包括表头和表体)
        for (const row of table.rows) {
            const cellToMove = row.cells[fromIndex]; // 获取要移动的单元格
            const targetCell = row.cells[toIndex]; // 获取目标位置的单元格

            row.removeChild(cellToMove); // 移除要移动的单元格

            // 将其插入到目标位置
            if (targetCell) {
                 row.insertBefore(cellToMove, targetCell);
            } else {
                 row.appendChild(cellToMove);
            }
        }
    }
    
    // 假设您的表头是页面加载时就存在的，或者在某个时机动态生成的
    // 您需要在表头渲染完毕后调用 enableColumnDragging()
    // 例如，如果您是通过JS动态填充表头的，就在填充完成后调用
    // 这里我们用一个MutationObserver来监听表头内容的变化，以确保表头存在后再绑定事件
    const observer = new MutationObserver((mutationsList, observer) => {
        for(const mutation of mutationsList) {
            if (mutation.type === 'childList' && thead.querySelector('th')) {
                enableColumnDragging();
                observer.disconnect(); // 绑定成功后停止观察，避免重复绑定
                break;
            }
        }
    });

    observer.observe(thead, { childList: true });

    // 如果您的表头是静态的，可以直接调用
    // if (thead.querySelector('th')) {
    //     enableColumnDragging();
    // }

});


